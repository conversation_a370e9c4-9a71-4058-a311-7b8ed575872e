# BarrioCore Messages Configuration
# This file contains all messages used in the plugin
# Color codes can be used with the & symbol

messages:
  # General messages
  player_only: "&cOnly players can use this command."
  invalid_usage: "&cInvalid usage. Please use /<command> <usage>."
  no_permission: "&cYou do not have permission to use this command."
  command_failed: "&cC<PERSON><PERSON> failed. Please check console for details."
  config_reloaded: "&aConfiguration files have been reloaded successfully."
  reload_error: "&cError reloading configuration. Check console for details."

  # Barrio creation and management
  barrio_created: "&aBarrio created and teleported to spawn!"
  barrio_created_for: "&aBarrio created for %player%."
  barrio_deleted: "&aBarrio deleted successfully."
  barrio_delete_confirm: "&cWarning: Type /barrio delete again to confirm deletion of your barrio!"
  create_cooldown: "&cYou must wait %time% before creating another barrio."
  type_selection: "&aPlease select a barrio type."
  template_selected: "&aTemplate selected: %template%"
  max_barrios_reached: "&cYou have reached your maximum number of barrios (%max%)."
  multiverse_not_installed: "&cError: Multiverse-Core is not installed."
  world_not_found: "&cError: World not found."
  not_in_barrio: "&cYou must be in your barrio to use this command."
  not_your_barrio: "&cYou can only expand your own barrio!"
  no_template_selected: "&cYou must select a template first!"

  # Border management
  border_expanded: "&aBorder expanded to %size% blocks."
  border_set: "&aBorder set to %size% blocks."
  max_border_reached: "&cThe border has reached its maximum size."
  invalid_number: "&cInvalid number specified."

  # Player and barrio lookup
  player_not_found: "&cPlayer not found."
  barrio_not_found: "&cBarrio not found."
  no_barrios: "&cThis player doesn't have any barrios."
  no_own_barrio: "&cYou do not own a barrio yet."

  # Teleportation
  teleported: "&aTeleported to %player%'s barrio."
  teleported_home: "&aTeleported to barrio spawn."
  teleported_top_rated: "&aTeleported to the top-rated barrio: &6%barrio_name% &a(Rating: &6%rating%&a, Reviews: &6%count%&a)"
  teleported_top_rated_no_ratings: "&aTeleported to barrio: &6%barrio_name% &a(No ratings yet)"
  spawn_set: "&aBarrio spawn location has been set."
  server_spawn_set: "&aServer spawn location has been updated."

  # Ownership and permissions
  not_owner: "&cYou are not the owner of this barrio."
  no_permission_gadget: "&cYou must be the owner or trusted in this barrio to manage gadgets."
  no_permission_setspawn: "&cYou must be trusted or owner to set the spawn."
  no_moderation_access: "&cYou don't have moderation access in this barrio."
  invalid_permission_type: "&cInvalid permission type. Use 'visitors', 'residents', or 'trusted'."
  invalid_setting: "&cInvalid setting specified."
  invalid_gadget: "&cInvalid gadget specified."

  # Transfer related
  transfer_initiated: "&aTransfer initiated. Type /barrio transfer confirm to confirm."
  transfer_confirmed: "&aBarrio transferred to %player%."
  transfer_received: "&a%player% has transferred their barrio to you."
  barrio_transferred: "&aBarrio transferred to %player%."
  transfer_received_admin: "&aAdmin %admin% has transferred barrio %barrio_id% to you."
  barrio_transferred_admin: "&aBarrio %barrio_id% transferred to %player%."
  no_pending_transfer: "&cYou don't have any pending transfers to confirm."
  self_transfer: "&cYou cannot transfer a barrio to yourself."
  transfer_cancelled: "&cTransfer cancelled."
  transfer_confirm: "&aUse &e%command%&a to confirm the transfer."

  # Member management
  rank_set: "&aPlayer %player%'s rank has been set to %rank%."
  rank_received: "&aYour rank in %owner%'s barrio has been set to %rank%."
  player_banned: "&aPlayer %player% has been banned from your barrio!"
  player_unbanned: "&aPlayer %player% has been unbanned from your barrio!"
  player_kicked: "&aPlayer %player% has been kicked from your barrio!"
  cannot_ban_trusted: "&cYou cannot ban trusted or higher ranked players!"
  player_already_banned: "&cThis player is already banned from this barrio!"
  player_not_banned: "&cThis player is not banned from this barrio!"
  cannot_kick_trusted: "&cYou cannot kick trusted or higher ranked players!"
  player_banned_message: "&cYou are banned from this barrio!"
  banned_from_barrio: "&cYou are banned from this barrio and have been teleported to spawn!"
  cannot_modify_trusted: "&cYou cannot modify the rank of trusted players or the owner!"
  invalid_usage_members: "&cInvalid usage. Use /barrio members, /barrio members manage <player>, or /barrio members set <player> <rank>"

  # Mode related
  mode_changed: "&aBarrio mode changed to %mode%."
  invalid_mode: "&cInvalid mode. Use UNLIMITED, INACTIVITY, RENTING, or RENTING_OFF."
  invalid_time_format: "&cInvalid time format. Use number + s/m/h/d/w/mo/y."
  invalid_group_check: "&cInvalid group check. Use ALL, RESIDENT_UP, TRUSTED_UP, or OWNER_ONLY"
  not_renting_mode: "&cThis barrio is not in a renting mode."

  # Economy related
  rent_paid: "&aRent paid successfully: $%amount%."
  rent_toggled_on: "&aRent collection has been turned on. Your barrio will be maintained as long as you have enough money to pay rent."
  rent_toggled_off: "&cRent collection has been turned off. Your barrio will be deleted when the next rent check occurs."
  rent_extended: "&aRent period extended successfully! Paid $%amount%. Your barrio is now safe for %days% days."
  insufficient_funds_extend: "&cInsufficient funds to extend rent. You need $%amount%."
  vault_not_found: "&cVault not found. Required for RENTING mode."
  insufficient_funds: "&cInsufficient funds to pay rent."
  barrio_deleted_no_funds: "&cYour barrio is being deleted because you don't have enough money to pay the rent of $%amount%."
  barrio_deleted_rent_off: "&cYour barrio is being deleted because rent is turned off."
  rent_not_enough_money: "&cYou don't have enough money to extend the rent period. Required: %price%"
  rent_not_renting: "&cThis barrio is not in renting mode."
  rent_already_paid: "&cThis barrio's rent is already paid for the maximum period."
  rent_payment_due: "&cYour barrio rent payment is due in %days% days. Use /barrio rent extend to extend."
  rent_payment_overdue: "&cYour barrio rent payment is overdue! Use /barrio rent extend to pay or your barrio may be deleted."
  rent_payment_success: "&aRent payment successful. Next payment due: %date%"
  rent_payment_failed: "&cRent payment failed. Not enough money."
  rent_info: "&aRent: %price% per %time%. Next payment: %date%"

  # Command usage
  invalid_usage_gadget_setmessage: "&cUsage: /barrio gadget setmessage <title|chat> <message... | clear>"
  invalid_usage_admin_transfer: "&cUsage: %command%"
  invalid_usage_transfer: "&cUsage: %usage%"

  # Error messages
  barrio_id_not_found: "&cBarrio with ID %barrio_id% not found."
  player_never_played: "&cPlayer %player% has never played on this server."
  database_error: "&cA database error occurred. Please contact an administrator."
  transfer_failed_db: "&cTransfer failed due to a database error. Please try again later."
  cannot_set_own_rank: "&cYou cannot set your own rank."
  invalid_rank_set: "&cInvalid rank. Valid ranks are: %ranks%"
  cannot_modify_owner: "&cYou cannot modify the owner's rank."
  trusted_cannot_set_trusted: "&cTrusted players cannot set other players to trusted rank."
  cannot_modify_higher_rank: "&cYou cannot modify the rank of players with a higher rank than you."
  no_permission_set_rank: "&cYou don't have permission to set ranks in this barrio."

  # Barrio information
  info_barrio_list: "&aBarrios owned by %player%:"
  info_barrio_list_item: "&a%index%. Created on %date% %default_marker%"
  info_barrio_info_title: "&6%barrio_id% &7- &eOwned by %owner%"
  info_barrio_info_chat: |-
    &7Created: &f%creation_date%
    &7Border Size: &f%border_size%
    &7Mode: &f%mode%
  info_barrio_nickname: "&7Nickname: &f%nickname%"

  # Default barrio messages
  default_barrio_set: "&aYour default barrio has been set to this barrio."
  default_barrio_updated: "&aYour default barrio has been updated because your previous default was deleted."
  default_barrio_marker: "&a[Default]"

  # Gadget related messages
  gadgets_entry_title: "&aWelcome to the Barrio!" # Your desired default title
  gadgets_entry_subtitle: "&eEnjoy your stay!" # Optional: Your desired default subtitle
  gadgets_entry_chat: "&6[&eBarrio&6] &fYou have entered the barrio." # Your desired default chat message
  gadgets_creeper_toggle: "&aCreeper damage %status%."
  gadgets_ghast_toggle: "&aGhast damage %status%."
  gadgets_fire_toggle: "&aFire spread %status%."
  gadgets_enderman_toggle: "&aEnderman griefing %status%."
  gadgets_ravager_toggle: "&aRavager griefing %status%."
  gadgets_title_toggle: "&aEntry title message %status%."
  gadgets_chat_toggle: "&aEntry chat message %status%."
  gadgets_status_enabled: "&aenabled"
  gadgets_status_disabled: "&cdisabled"

  # Permission related messages
  permissions_visitor_updated: "&aVisitor permissions updated."
  permissions_resident_updated: "&aResident permissions updated."
  permissions_trusted_updated: "&aTrusted permissions updated."
  permissions_permission_set: "&aPermission '%permission%' set to %value% for %rank%."

  # GUI related messages
  gui_status_enabled: "&aEnabled"
  gui_status_disabled: "&cDisabled"

  # World settings messages
  no_permission_settings: "&cYou don't have permission to access barrio settings."
  settings_pvp_enabled: "&aPVP enabled for this barrio."
  settings_pvp_disabled: "&aPVP disabled for this barrio."
  settings_hunger_enabled: "&aHunger loss enabled for this barrio."
  settings_hunger_disabled: "&aHunger loss disabled for this barrio."
  settings_time_changed: "&aTime mode set to: %mode%"
  settings_weather_changed: "&aWeather mode set to: %mode%"
  settings_difficulty_changed: "&aDifficulty set to: %mode%"
  settings_spawn_enabled: "&aUse barrio spawn enabled."
  settings_spawn_disabled: "&aUse barrio spawn disabled."
  settings_respawn_server: "&aRespawn location set to: Server Spawn"
  settings_respawn_barrio: "&aRespawn location set to: Barrio Spawn"
  settings_nickname_changed: "&aBarrio nickname set to: %name%"
  settings_barrio_chat_enabled: "&aBarrio chat only mode %status%."

  # Rating related
  rating:
    specify_barrio: "&cPlease specify a player name to rate their barrio, or use /barrio rate while in a barrio."
    barrio_not_found: "&cBarrio not found. Please check the player name and index and try again."
    cannot_rate_own: "&cYou cannot rate your own barrio."
    cooldown: "&cYou must wait %time% before rating this barrio again."
    invalid_rating: "&cRating must be between %min% and %max% stars."
    invalid_rating_format: "&cInvalid rating format. Please use a number between 1 and 5."
    success: "&aYou rated %barrio_name% with %stars% stars!"
    error: "&cAn error occurred while submitting your rating. Please try again."
    no_stars_selected: "&cPlease select a star rating before submitting."
    no_top_rated: "&cNo barrios found. Create a barrio first or make sure at least one barrio allows visitors."
    current_barrio: "&aRating the barrio you are currently in."
