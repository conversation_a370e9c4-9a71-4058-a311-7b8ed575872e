name: BarrioCore
version: '${project.version}'
main: me.zivush.barriocore.BarrioCore
api-version: '1.21'
depend: [Multiverse-Core]
softdepend: [PlaceholderAPI]
commands:
  barrio:
    description: Manage barrios
    usage: |-
      /<command> - Open barrio type selection GUI
      /<command> create - Open barrio type selection GUI
      /<command> create regular - Create a regular barrio
      /<command> create template - Create a barrio from a template
      /<command> expand - Expand your barrio border
      /<command> admin expand <size> - Set barrio border size (admin only)
      /<command> admin create <regular/template> <player> - Create a barrio for a player (admin only)
      /<command> admin delete <player> <index> - Delete a player's barrio (admin only)
      /<command> admin transfer <player> - Transfer current barrio to a player (admin only)
      /<command> admin teleport <player> <index> - Teleport to a player's barrio (admin only)
      /<command> visit|tp <player> <index> - Visit a player's barrio
      /<command> visit|tp top-rated - Visit the top-rated barrio
      /<command> transfer <player> - Transfer your barrio to another player
      /<command> transfer confirm - Confirm barrio transfer
      /<command> members set <player> <visitor|resident|trusted> - Set a player's rank in your barrio
      /<command> admin mode <UNLIMITED|INACTIVITY|RENTING> [time] [group-check/amount] [online-in-world]
      /<command> admin setserverspawn - Set the server spawn location (admin only)
      /<command> admin setspawn - Set spawn location in current barrio (admin only)
      /<command> admin settings <setting> <toggle> - Toggles the specified setting in the current barrio (admin only)
      /<command> admin gadgets <gadget> <toggle> - Toggles the specified gadget in the current barrio (admin only)
      /<command> admin reload - Reload the plugin configuration files (admin only)
      /<command> ban <player> - Ban a player from your barrio
      /<command> unban <player> - Unban a player from your barrio
      /<command> kick <player> - Kick a player from your barrio
      /<command> info - Display barrio information
      /<command> setspawn - Set barrio spawn location
      /<command> setdefault - Set current barrio as your default barrio
      /<command> spawn - Teleport to current barrio's spawn location
      /<command> home [index] - Teleport to the spawn of the barrio they own (uses default if no index specified)
      /<command> gadget - Allows owners/trusted members to open the barrio gadgets GUI.
      /<command> rent <on|off> - Toggle rent collection on or off for your barrio
      /<command> rent extend - Extend your barrio rent period by paying the rent price
      /<command> settings - Open the player world settings GUI
      /<command> upgrades - Open the barrio upgrades GUI
      /<command> delete - Delete your own barrio (owner only)
      /<command> rate <barrio_id> [stars] [message] - Rate a barrio
      /<command> ratings [barrio_id] - View ratings for a barrio

permissions:
  barrio.create.*:
    description: Allows creation of unlimited barrios
    default: op
  barrio.create.0:
    description: Allows creation of 0 barrios
    default: false
  barrio.create.1:
    description: Allows creation of 1 barrio (applies to regular or template)
    default: true
  barrio.create.2:
    description: Allows creation of 2 barrios (applies to regular or template)
    default: false
  barrio.cmd.expand:
    description: Allows players to expand their barrio border
    default: true
  barrio.admin.expand:
    description: Allows admins to set barrio borders
    default: op
  barrio.admin.create.regular:
    description: Allows admins to create regular barrios for players
    default: op
  barrio.admin.create.template:
    description: Allows admins to create template barrios for players
    default: op
  barrio.admin.delete:
    description: Allows admins to delete player barrios
    default: op
  barrio.admin.transfer:
    description: Allows admins to transfer barrio ownership
    default: op
  barrio.admin.teleport:
    description: Allows admins to teleport to any barrio
    default: op
  barrio.cmd.visit:
    description: Allows players to visit other barrios
    default: true
  barrio.cmd.transfer:
    description: Allows players to transfer their barrio ownership
    default: true
  barrio.cmd.transfer.confirm:
    description: Allows players to confirm barrio transfer
    default: true
  barrio.cmd.members:
    description: Allows players to manage barrio member ranks
    default: true
  barrio.cmd.ban:
    description: Allows players to ban players from their barrio
    default: true
  barrio.cmd.unban:
    description: Allows players to unban players from their barrio
    default: true
  barrio.cmd.kick:
    description: Allows players to kick players from their barrio
    default: true
  barrio.cmd.members.trusted:
    description: Allows trusted players to manage visitor and resident ranks
    default: true
  barrio.cmd.gadget:
    description: Allows owners/trusted members to open the barrio gadgets GUI.
    default: true
  barrio.cmd.info:
    description: Allows players to view barrio information
    default: true
  barrio.cmd.setspawn:
    description: Allows trusted members and owners to set barrio spawn
    default: true
  barrio.cmd.home:
    description: Allows players to teleport to their barrio spawn
    default: true
  barrio.cmd.spawn:
    description: Allows players to teleport to current barrio's spawn
    default: true
  barrio.cmd.rent:
    description: Allows barrio owners to toggle rent collection on or off
    default: true
  barrio.cmd.settings:
    description: Allows owners and trusted members to access the world settings GUI
    default: true
  barrio.cmd.rate:
    description: Allows players to rate barrios
    default: true
  barrio.cmd.ratings:
    description: Allows players to view barrio ratings
    default: true
  barrio.cmd.visit.toprated:
    description: Allows players to visit the top-rated barrio
    default: true
  barrio.cmd.delete:
    description: Allows barrio owners to delete their own barrio
    default: true
  barrio.settings.*:
    description: Allows admins to toggle all settings in any barrio
    default: op
  barrio.gadgets.*:
    description: Allows admins to toggle all gadgets in any barrio
    default: op
  barrio.admin.reload:
    description: Allows admins to reload the plugin configuration
    default: op
  barrio.admin.setspawn:
    description: Allows admins to set spawn in any barrio
    default: op
  barrio.admin.bypass:
    description: Allows admins to bypass all barrio permission checks
    default: op