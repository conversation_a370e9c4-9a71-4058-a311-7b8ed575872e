package me.zivush.barriocore.gui;

import de.rapha149.signgui.SignGUI;
import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.util.HeadUtils;
import me.zivush.barriocore.worldsettings.BarrioWorldSettings;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Difficulty;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.WeatherType;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

public class BarrioWorldSettingsGUI extends BaseGUI {
    private final String barrioId;
    private final BarrioWorldSettings settings;

    public BarrioWorldSettingsGUI(BarrioCore plugin, Player player, String barrioId) {
        super(plugin, player, "barrio_world_settings_gui");
        this.barrioId = barrioId;
        this.settings = plugin.getWorldSettingsManager().getWorldSettings(barrioId);
        setupSettingsItems();
    }

    @Override
    protected void setupItems() {

    }
    protected void setupSettingsItems() {
        // Clear inventory first
        inventory.clear();

        // Setup decoration items
        setupDecoration();

        // Setup all buttons from config
        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) {
            plugin.getLogger().warning("GUI configuration for '" + guiName + "' not found in gui.yml");
            return;
        }

        for (String key : guiSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("decoration")) {
                continue; // Skip meta keys
            }

            ConfigurationSection buttonSection = guiSection.getConfigurationSection(key);
            if (buttonSection == null) continue;

            // Create the button with current status
            ItemStack button = createButtonWithStatus(buttonSection, key);

            // Set the button in the inventory
            List<Integer> slots = buttonSection.getIntegerList("slots");
            for (int slot : slots) {
                if (slot >= 0 && slot < inventory.getSize()) {
                    inventory.setItem(slot, button);
                }
            }
        }
    }

    private ItemStack createButtonWithStatus(ConfigurationSection buttonSection, String key) {
        ItemStack button = createItem(buttonSection);
        ItemMeta meta = button.getItemMeta();
        if (meta == null) return button;

        // Get the current status based on the button key
        String status = getStatusForButton(key);

        // Replace status placeholder in name and lore
        String name = meta.getDisplayName();
        if (name != null && name.contains("%status%")) {
            meta.setDisplayName(name.replace("%status%", status));
        }

        List<String> lore = meta.getLore();
        if (lore != null) {
            List<String> newLore = new ArrayList<>();
            for (String line : lore) {
                newLore.add(line.replace("%status%", status));
            }
            meta.setLore(newLore);
        }

        // Special handling for player head
        if (key.equals("player_head") && button.getType() == Material.PLAYER_HEAD) {
            UUID ownerUuid = getBarrioOwnerUuid(barrioId);
            if (ownerUuid != null) {
                OfflinePlayer owner = Bukkit.getOfflinePlayer(ownerUuid);
                button = HeadUtils.setSkullOwner(button, owner);
                return button;
            }
        }

        button.setItemMeta(meta);
        return button;
    }

    private String getStatusForButton(String key) {
        switch (key) {
            case "pvp_toggle":
                return settings.isPvpEnabled() ?
                    ChatColor.GREEN + "Enabled" : ChatColor.RED + "Disabled";
            case "hunger_toggle":
                return settings.isHungerLossEnabled() ?
                    ChatColor.GREEN + "Enabled" : ChatColor.RED + "Disabled";
            case "time_toggle":
                return ChatColor.YELLOW + settings.getTimeMode().name();
            case "weather_toggle":
                return ChatColor.YELLOW + settings.getWeatherMode().name();
            case "difficulty_toggle":
                return ChatColor.YELLOW + settings.getDifficulty().name();
            case "use_spawn_toggle":
                return settings.isUseBarrioSpawn() ?
                    ChatColor.GREEN + "Enabled" : ChatColor.RED + "Disabled";
            case "respawn_location_toggle":
                return settings.isUseServerSpawn() ?
                    ChatColor.YELLOW + "Server Spawn" : ChatColor.YELLOW + "Barrio Spawn";
            case "barrio_chat_toggle":
                return settings.isBarrioChatOnlyEnabled() ?
                    ChatColor.GREEN + "Enabled" : ChatColor.RED + "Disabled";
            case "rent_toggle":
                return settings.isRentEnabled() ?
                    ChatColor.GREEN + "Enabled" : ChatColor.RED + "Disabled";
            case "nickname_change":
                String nickname = getBarrioNickname(barrioId);
                if (nickname != null && !nickname.isEmpty()) {
                    return ChatColor.YELLOW + nickname;
                } else {
                    // Get the owner's name if possible
                    UUID ownerUuid = getBarrioOwnerUuid(barrioId);
                    String ownerName = "Unknown";
                    if (ownerUuid != null) {
                        ownerName = Bukkit.getOfflinePlayer(ownerUuid).getName();
                        if (ownerName == null) ownerName = "Unknown";
                    }
                    return ChatColor.GRAY + "Default Name";
                }
            default:
                return "";
        }
    }

    private UUID getBarrioOwnerUuid(String barrioId) {
        return plugin.getWorldSettingsManager().getBarrioOwnerUuid(barrioId);
    }

    /**
     * Gets the nickname for a barrio from the cached data.
     *
     * @param barrioId The ID of the barrio
     * @return The nickname, or null if not set
     */
    private String getBarrioNickname(String barrioId) {
        return plugin.getWorldSettingsManager().getBarrioNickname(barrioId);
    }

    /**
     * Updates the nickname for a barrio in the cached data.
     *
     * @param barrioId The ID of the barrio
     * @param nickname The new nickname
     */
    private void updateBarrioNickname(String barrioId, String nickname) {
        plugin.getWorldSettingsManager().updateBarrioNickname(barrioId, nickname);
    }

    public void handleClick(InventoryClickEvent event) {
        int clickedSlot = event.getSlot();
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null) return;

        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) return;

        // Find which button was clicked
        for (String key : guiSection.getKeys(false)) {
            if (key.equals("title") || key.equals("size") || key.equals("decoration")) {
                continue; // Skip meta keys
            }

            ConfigurationSection buttonSection = guiSection.getConfigurationSection(key);
            if (buttonSection == null) continue;

            List<Integer> slots = buttonSection.getIntegerList("slots");
            if (slots.contains(clickedSlot)) {
                handleButtonClick(key, buttonSection);
                break;
            }
        }
    }

    private void handleButtonClick(String key, ConfigurationSection buttonSection) {
        switch (key) {
            case "back_button":
                player.closeInventory();
                Bukkit.dispatchCommand(player, "barrio");
                break;
            case "guest_permissions":
                openPermissionsGUI("visitors");
                break;
            case "resident_permissions":
                openPermissionsGUI("residents");
                break;
            case "trusted_permissions":
                openPermissionsGUI("trusted");
                break;
            case "player_head":
                openMembersListGUI();
                break;
            case "rent_toggle":
                toggleRent();
                break;
            case "rent_extend":
                extendRent();
                break;
            case "nickname_change":
                openNicknamePrompt();
                break;
            case "delete_barrio":
                confirmDeleteBarrio();
                break;
            case "pvp_toggle":
                togglePvp();
                break;
            case "hunger_toggle":
                toggleHunger();
                break;
            case "time_toggle":
                cycleTimeMode();
                break;
            case "weather_toggle":
                cycleWeatherMode();
                break;
            case "difficulty_toggle":
                cycleDifficulty();
                break;
            case "use_spawn_toggle":
                toggleUseSpawn();
                break;
            case "respawn_location_toggle":
                toggleRespawnLocation();
                break;
            case "barrio_chat_toggle":
                toggleBarrioChat();
                break;
            case "upgrades_button":
                openUpgradesGUI();
                break;
        }

        // Update the GUI
        setupSettingsItems();
    }

    private void openPermissionsGUI(String type) {
        player.closeInventory();

        // Use the barrio permissions command instead of directly creating GUI instances
        Bukkit.dispatchCommand(player, "barrio permissions " + type);
    }

    private void openMembersListGUI() {
        player.closeInventory();
        BarrioMembersListGUI gui = new BarrioMembersListGUI(plugin, player, barrioId);
        plugin.setOpenGui(player.getUniqueId(), gui);
        gui.open();
    }

    private void toggleRent() {
        settings.setRentEnabled(!settings.isRentEnabled());
        player.sendMessage(settings.isRentEnabled() ?
            plugin.getMessage("messages.rent_toggled_on") :
            plugin.getMessage("messages.rent_toggled_off"));

        // Update the GUI to reflect the changes
        setupSettingsItems();
    }

    private void extendRent() {
        // Execute the rent extend command
        player.closeInventory();
        Bukkit.dispatchCommand(player, "barrio rent extend");
    }

    private void openNicknamePrompt() {
        player.closeInventory();

        // Get current nickname or default
        String currentNickname = getBarrioNickname(barrioId);
        if (currentNickname == null) {
            currentNickname = "";
        }

        try {
            // Open sign GUI with the new API
            SignGUI.builder()
                .setLines(
                    "§6Enter Barrio Name",
                    currentNickname,
                    "",
                    "§7Max 16 characters"
                )
                .setType(Material.OAK_SIGN)
                .setHandler((p, result) -> {
                    String newName = result.getLine(1).trim();
                    if (!newName.isEmpty()) {
                        if (newName.length() > 16) {
                            newName = newName.substring(0, 16);
                        }
                        updateBarrioNickname(barrioId, newName);
                        player.sendMessage(plugin.getMessage("messages.settings_nickname_changed").replace("%name%", newName));
                    }

                    // Reopen the settings GUI
                    Bukkit.getScheduler().runTaskLater(plugin, () -> {
                        BarrioWorldSettingsGUI gui = new BarrioWorldSettingsGUI(plugin, player, barrioId);
                        plugin.setOpenGui(player.getUniqueId(), gui);
                        gui.open();
                    }, 2L);

                    return Collections.emptyList(); // Just close the sign
                })
                .build()
                .open(player);
        } catch (Exception e) {
            plugin.getLogger().severe("Error opening SignGUI: " + e.getMessage());
            player.sendMessage(ChatColor.RED + "Error opening sign editor. Please try again later.");

            // Reopen the settings GUI if there was an error
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                BarrioWorldSettingsGUI gui = new BarrioWorldSettingsGUI(plugin, player, barrioId);
                plugin.setOpenGui(player.getUniqueId(), gui);
                gui.open();
            }, 2L);
        }
    }

    // Track last click time for double-click detection
    private long lastDeleteClickTime = 0;

    private void confirmDeleteBarrio() {
        long currentTime = System.currentTimeMillis();
        long timeSinceLastClick = currentTime - lastDeleteClickTime;

        // Check if this is a double click (within 500ms)
        if (timeSinceLastClick < 500) {
            // This is a double click, execute the delete command
            player.closeInventory();
            Bukkit.dispatchCommand(player, "barrio delete");
        } else {
            // First click, show warning message
            player.sendMessage(plugin.getMessage("messages.barrio_delete_confirm"));
            lastDeleteClickTime = currentTime;
        }
    }

    private void togglePvp() {
        settings.setPvpEnabled(!settings.isPvpEnabled());
        player.sendMessage(settings.isPvpEnabled() ?
            plugin.getMessage("messages.settings_pvp_enabled") :
            plugin.getMessage("messages.settings_pvp_disabled"));

        // Update the GUI to reflect the changes
        setupSettingsItems();
    }

    private void toggleHunger() {
        settings.setHungerLossEnabled(!settings.isHungerLossEnabled());
        player.sendMessage(settings.isHungerLossEnabled() ?
            plugin.getMessage("messages.settings_hunger_enabled") :
            plugin.getMessage("messages.settings_hunger_disabled"));

        // Update the GUI to reflect the changes
        setupSettingsItems();
    }

    private void cycleTimeMode() {
        BarrioWorldSettings.TimeMode[] modes = BarrioWorldSettings.TimeMode.values();
        int currentIndex = Arrays.asList(modes).indexOf(settings.getTimeMode());
        int nextIndex = (currentIndex + 1) % modes.length;
        settings.setTimeMode(modes[nextIndex]);
        player.sendMessage(plugin.getMessage("messages.settings_time_changed")
            .replace("%mode%", modes[nextIndex].name()));

        // Update the GUI to reflect the changes
        setupSettingsItems();
    }

    private void cycleWeatherMode() {
        BarrioWorldSettings.WeatherMode[] modes = BarrioWorldSettings.WeatherMode.values();
        int currentIndex = Arrays.asList(modes).indexOf(settings.getWeatherMode());
        int nextIndex = (currentIndex + 1) % modes.length;
        settings.setWeatherMode(modes[nextIndex]);

        // Set the fixed weather type based on the mode
        switch (modes[nextIndex]) {
            case CLEAR:
                settings.setFixedWeather(WeatherType.CLEAR);
                break;
            case STORM:
            case RAIN:
                settings.setFixedWeather(WeatherType.DOWNFALL);
                break;
            default:
                // OFF mode - don't change fixed weather
                break;
        }

        player.sendMessage(plugin.getMessage("messages.settings_weather_changed")
            .replace("%mode%", modes[nextIndex].name()));

        // Update the GUI to reflect the changes
        setupSettingsItems();
    }

    private void cycleDifficulty() {
        Difficulty[] difficulties = Difficulty.values();
        int currentIndex = Arrays.asList(difficulties).indexOf(settings.getDifficulty());
        int nextIndex = (currentIndex + 1) % difficulties.length;
        settings.setDifficulty(difficulties[nextIndex]);
        player.sendMessage(plugin.getMessage("messages.settings_difficulty_changed")
            .replace("%mode%", difficulties[nextIndex].name()));

        // Update the GUI to reflect the changes
        setupSettingsItems();
    }

    private void toggleUseSpawn() {
        settings.setUseBarrioSpawn(!settings.isUseBarrioSpawn());
        player.sendMessage(settings.isUseBarrioSpawn() ?
            plugin.getMessage("messages.settings_spawn_enabled") :
            plugin.getMessage("messages.settings_spawn_disabled"));

        // Update the GUI to reflect the changes
        setupSettingsItems();
    }

    private void toggleRespawnLocation() {
        settings.setUseServerSpawn(!settings.isUseServerSpawn());
        player.sendMessage(settings.isUseServerSpawn() ?
            plugin.getMessage("messages.settings_respawn_server") :
            plugin.getMessage("messages.settings_respawn_barrio"));

        // Update the GUI to reflect the changes
        setupSettingsItems();
    }

    private void toggleBarrioChat() {
        settings.setBarrioChatOnly(!settings.isBarrioChatOnlyEnabled());
        player.sendMessage(plugin.getMessage("messages.settings_barrio_chat_enabled")
            .replace("%status%", settings.isBarrioChatOnlyEnabled() ?
                plugin.getMessage("messages.gadgets_status_enabled") :
                plugin.getMessage("messages.gadgets_status_disabled")));

        // Update the GUI to reflect the changes
        setupSettingsItems();
    }
}
