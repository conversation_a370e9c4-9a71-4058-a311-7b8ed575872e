package me.zivush.barriocore.gui;

import me.zivush.barriocore.BarrioCore;
import me.zivush.barriocore.Database;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;

public class G<PERSON><PERSON>istener implements Listener {
    private final BarrioCore plugin;
    private final Database database;

    public GUIListener(BarrioCore plugin, Database database) {
        this.plugin = plugin;
        this.database = database;
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();

        // Get the GUI from the player's UUID
        BaseGUI gui = plugin.getOpenGui(player.getUniqueId());
        if (gui == null) return;

        // Check if this is the player's current inventory
        // We don't compare with gui.getInventory() directly since it might have been updated
        if (event.getView().getTopInventory() != event.getClickedInventory()) return;

        // Cancel all clicks by default to prevent item removal
        event.setCancelled(true);

        // First check if there's a command to execute for this slot
        // This allows any GUI item to have an execute command without modifying each GUI class
        if (gui.checkAndExecuteCommandForSlot(event)) {
            return; // Command was executed, no need to process further
        }

        // Handle the click in the appropriate GUI
        if (gui instanceof BarrioCreateRegularGUI) {
            ((BarrioCreateRegularGUI) gui).handleClick(event);
        } else if (gui instanceof BarrioCreateTemplateGUI) {
            ((BarrioCreateTemplateGUI) gui).handleClick(event);
        } else if (gui instanceof BarrioTypeGUI) {
            ((BarrioTypeGUI) gui).handleClick(event);
        } else if (gui instanceof BarrioGadgetsGUI) { // Added block
            ((BarrioGadgetsGUI) gui).handleClick(event);
        } else if (gui instanceof BarrioUpgradesGUI) {
            ((BarrioUpgradesGUI) gui).handleClick(event);
        } else if (gui instanceof BarrioBlockUpgradesGUI) {
            ((BarrioBlockUpgradesGUI) gui).handleClick(event);
        } else if (gui instanceof BarrioEntityUpgradesGUI) {
            ((BarrioEntityUpgradesGUI) gui).handleClick(event);
        } else if (gui instanceof BarrioPermissionsGUI) {
            ((BarrioPermissionsGUI) gui).handleClick(event);
        } else if (gui instanceof BarrioResidentPermissionsEditGUI) {
            ((BarrioResidentPermissionsEditGUI) gui).handleClick(event);
        } else if (gui instanceof BarrioTrustedPermissionsEditGUI) {
            ((BarrioTrustedPermissionsEditGUI) gui).handleClick(event);
        } else if (gui instanceof BarrioResidentPermissionsGUI) {
            ((BarrioResidentPermissionsGUI) gui).handleClick(event);
        } else if (gui instanceof BarrioMemberManageGUI) {
            ((BarrioMemberManageGUI) gui).handleClick(event);
        } else if (gui instanceof BarrioMembersListGUI) {
            ((BarrioMembersListGUI) gui).handleClick(event);
        } else if (gui instanceof BarrioWorldSettingsGUI) {
            ((BarrioWorldSettingsGUI) gui).handleClick(event);
        } else if (gui instanceof BarrioMainGUI) {
            ((BarrioMainGUI) gui).handleClick(event);
        } else if (gui instanceof BarrioRatingGUI) {
            ((BarrioRatingGUI) gui).handleClick(event);
        } else if (gui instanceof BarrioRatingInfoGUI) {
            ((BarrioRatingInfoGUI) gui).handleClick(event);
        }
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) return;
        Player player = (Player) event.getPlayer();

        // Get the GUI from the player's UUID
        BaseGUI gui = plugin.getOpenGui(player.getUniqueId());
        if (gui == null) return;

        // Check if this is the player's open GUI
        if (event.getInventory().equals(gui.getInventory())) {
            // We need to keep track of GUIs that are temporarily closed for sign editing

            // For BarrioRatingGUI, we'll handle re-registration in the sign editor callback
            // For other GUIs, remove the reference as usual
            if (!(gui instanceof BarrioRatingGUI)) {
                plugin.setOpenGui(player.getUniqueId(), null);
            }
            // For BarrioRatingGUI, we keep the reference during sign editing
        }
    }
}
