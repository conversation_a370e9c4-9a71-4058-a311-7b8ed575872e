package me.zivush.barriocore.gui;

import me.zivush.barriocore.BarrioCore;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.configuration.ConfigurationSection;

import java.util.List;

/**
 * Main upgrades GUI that shows options to access block and entity upgrades.
 */
public class BarrioUpgradesGUI extends BaseGUI {
    
    private final String barrioId;

    public BarrioUpgradesGUI(BarrioCore plugin, Player player, String barrioId) {
        super(plugin, player, "barrio_upgrades_main");
        this.barrioId = barrioId;
    }

    public void handleClick(InventoryClickEvent event) {
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null) return;

        ConfigurationSection guiSection = plugin.getGuiConfig().getConfigurationSection(guiName);
        if (guiSection == null) return;

        int clickedSlot = event.getSlot();

        // Check for back button click
        ConfigurationSection backButtonSection = guiSection.getConfigurationSection("back_button");
        if (backButtonSection != null) {
            List<Integer> backButtonSlots = backButtonSection.getIntegerList("slots");
            if (backButtonSlots.contains(clickedSlot)) {
                player.closeInventory();
                // Go back to settings menu
                org.bukkit.Bukkit.dispatchCommand(player, "barrio settings");
                return;
            }
        }

        // Check for block upgrades button
        ConfigurationSection blockUpgradesSection = guiSection.getConfigurationSection("block_upgrades_button");
        if (blockUpgradesSection != null) {
            List<Integer> blockUpgradesSlots = blockUpgradesSection.getIntegerList("slots");
            if (blockUpgradesSlots.contains(clickedSlot)) {
                player.closeInventory();
                // Open block upgrades GUI
                BarrioBlockUpgradesGUI blockUpgradesGUI = new BarrioBlockUpgradesGUI(plugin, player, barrioId);
                plugin.setOpenGui(player.getUniqueId(), blockUpgradesGUI);
                blockUpgradesGUI.open();
                return;
            }
        }

        // Check for entity upgrades button
        ConfigurationSection entityUpgradesSection = guiSection.getConfigurationSection("entity_upgrades_button");
        if (entityUpgradesSection != null) {
            List<Integer> entityUpgradesSlots = entityUpgradesSection.getIntegerList("slots");
            if (entityUpgradesSlots.contains(clickedSlot)) {
                player.closeInventory();
                // Open entity upgrades GUI
                BarrioEntityUpgradesGUI entityUpgradesGUI = new BarrioEntityUpgradesGUI(plugin, player, barrioId);
                plugin.setOpenGui(player.getUniqueId(), entityUpgradesGUI);
                entityUpgradesGUI.open();
                return;
            }
        }
    }

    public String getBarrioId() {
        return barrioId;
    }
}
